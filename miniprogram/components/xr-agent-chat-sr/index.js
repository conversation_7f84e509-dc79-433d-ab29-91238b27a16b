const app = getApp()

const getToken = require("../../utils/token").getToken
const SpeechTranscription = require("../../utils/st")
const SpeechRecognition = require("../../utils/sr")
let startTimestamp

Component({

  /**
   * 组件的属性列表
   */
  properties: {
    isLoadingRaw: Boolean
  },

  /**
   * 组件的初始数据
   */
  data: {
    isLoading: false,
    srStart : false,
    srResult : "未开始识别",
    srInputFinal: '',
    srCurrentWords: ''
  },

  lifetimes: {
    async attached() {
      await this.setUpSr()
    },
    detached() {
      this.destroySr()
    }
  },

  observers: {
    isLoadingRaw(newVal) {
      this.setData({
        isLoading: newVal
      })
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    async setUpSr() {
      wx.getRecorderManager().onFrameRecorded((res)=>{
        if (res.isLastFrame) {
            console.log("record done")
        }
        if (this.data.sr && this.data.srStart) {
            console.log("send " + res.frameBuffer.byteLength)
            this.data.sr.sendAudio(res.frameBuffer)
        }
    })
      wx.getRecorderManager().onStart(()=>{
          console.log("start recording...")
      })
      wx.getRecorderManager().onStop((res) => {
          console.log("stop recording...")
          if (res.tempFilePath) {
              wx.removeSavedFile({
                  filePath:res.tempFilePath
              })
          }
      })
      wx.getRecorderManager().onError((res) => {
          console.log("recording failed:" + res)
      })

      try {
        if (!this.data.token) {
          // const token = await getToken(app.globalData.AKID,
          //   app.globalData.AKKEY)
          const token = '4765f25a47f74189b3b2e1f4dca50aff'
            this.setData({
              token: token
            })
        }
      } catch (e) {
          console.log("error on get token:", JSON.stringify(e))
          return
      }
      
      let sr = new SpeechTranscription({
          url : app.globalData.URL,
          appkey: app.globalData.APPKEY,
          token: this.data.token,
      })

      sr.on("started", async (msg)=> {
          console.log("Client recv started")
          console.log("预热："+(Date.now()-startTimestamp)/1000+'s')
          await this.sleep(100)
          this.setData({
              srResult : msg,
              srStart: true
          })
          wx.showToast({
            title: '请开始说话'
          })
          this.triggerEvent('onSrStart')
      })

      sr.on("changed", (msg)=>{
          console.log("Client recv changed:", msg)
          this.setData({
            srCurrentWords: JSON.parse(msg).payload.result
          })
      })
    
      sr.on("completed", (msg)=>{
          console.log("Client recv completed:", msg)
          this.setData({
              srResult : msg,
          })
          this.triggerEvent('onSrInput', {
            message: this.data.srInputFinal
          })
          wx.showToast({
            title: '语音识别成功'
          })
      })
  
      sr.on("end", (msg)=>{
        console.log("Client recv sentenceEnd:", msg)
        const {result} = JSON.parse(msg).payload
        const voiceInputFinal = this.data.srInputFinal + result
        this.setData({
            stResult : msg,
            srInputFinal: voiceInputFinal
        })
      })

      sr.on("closed", () => {
          console.log("Client recv closed")
      })

      sr.on("failed", (msg)=>{
          console.log("Client recv failed:", msg)
          wx.showToast({
            title: '未识别到完整语音',
            icon: "error"
          })
      })

      this.data.sr = sr
    },

    destroySr() {
      console.log("sr detached")
      this.setData({
        srStart: false
      })
      wx.getRecorderManager().stop()
      if (this.data.sr) {
          this.data.sr.shutdown()
      } else {
          console.log("sr is null")
      }
    },

    async onSrStart () {
      if (!this.data.sr) {
          console.log("sr is null")
          return
      }
    
      if (this.data.srStart) {
          console.log("sr is started!")
          return
      }
      let sr = this.data.sr
      try {
        // wx.showToast({
        //   title: '请稍候'
        // })
          startTimestamp = Date.now()
          await sr.start(sr.defaultStartParams())
      } catch (e) {
          console.log("start failed:" + e)
          return
      }
    
      wx.getRecorderManager().start({
          duration: 600000,
          numberOfChannels: 1,
          sampleRate : 16000,
          format: "PCM",
          frameSize: 4
      })
    },

    async sleep(ms) {
      return new Promise(r => setTimeout(r, ms));
    },
    
    async onSrStop() {
      wx.getRecorderManager().stop()
      // wx.showToast({
      //   title: '请稍候'
      // })
      await this.sleep(500)
      if (this.data.srStart && this.data.sr) {
          try {
              console.log("prepare close sr")
              await this.data.sr.close()
          } catch(e) {
              console.log("close sr failed:" + e)
              this.data.sr.shutdown()
          }
          this.setData({
            srStart: false,
            srInputFinal: ''
          })
      }
    },

    async toggleSr() {
      if (this.data.srStart) {
        await this.onSrStop()
      } else {
        await this.onSrStart()
      }
    }
  }
})